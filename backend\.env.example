# Database Configuration
MONGODB_URI=mongodb://localhost:27017/invonest
# For production (MongoDB Atlas):
# MONGODB_URI=mongodb+srv://username:<EMAIL>/invonest?retryWrites=true&w=majority

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_minimum_32_characters
JWT_REFRESH_SECRET=your_super_secret_refresh_key_here_minimum_32_characters
JWT_EXPIRE=7d

# Server Configuration
PORT=5000
NODE_ENV=development

# CORS Configuration
FRONTEND_URL=http://localhost:3000
# For production: https://your-app.vercel.app

# Email Configuration (for OTP and notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
MAIL_USER=<EMAIL>
MAIL_PASS=your_app_password

# reCAPTCHA v3 Configuration
# Get your secret key from https://www.google.com/recaptcha/admin
RECAPTCHA_SECRET_KEY=your_recaptcha_secret_key_here
RECAPTCHA_MIN_SCORE=0.5

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security Configuration
BCRYPT_ROUNDS=12



# Blockchain Configuration (Optional - for invoice verification)
POLYGON_RPC_URL=https://rpc-mumbai.maticvigil.com
PRIVATE_KEY=your_wallet_private_key

# OpenAI Configuration (Optional - for AI features)
OPENAI_API_KEY=your_openai_api_key
