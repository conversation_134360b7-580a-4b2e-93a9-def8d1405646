services:
  - type: web
    name: invonest-backend
    env: node
    region: singapore
    plan: free
    buildCommand: cd backend && npm install && npm run build
    startCommand: cd backend && npm start
    healthCheckPath: /api/health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 5000
      - key: MONGODB_URI
        value: mongodb+srv://guptaa1289:<EMAIL>/invonest
      - key: JWT_SECRET
        value: invonest-super-secret-jwt-key-2024-production-32chars
      - key: JWT_REFRESH_SECRET
        value: invonest-refresh-secret-jwt-key-2024-production-32chars
      - key: JWT_EXPIRE
        value: 7d
      - key: FRONTEND_URL
        value: https://invonest.vercel.app
      - key: RAZORPAY_KEY_ID
        value: rzp_test_FZjGGkxRPPju8I
      - key: RAZORPAY_KEY_SECRET
        value: I1OgjfBYbkzjzO4kHGI28jOQ
      - key: MAIL_HOST
        value: smtp.gmail.com
      - key: MAIL_PORT
        value: 587
      - key: MAIL_USER
        value: <EMAIL>
      - key: MAIL_PASS
        value: aery myha nqrl vaex
      - key: RECAPTCHA_SECRET_KEY
        value: 6LfjK4grAAAAAB2GkZptDqpOV8T_hiausJHMu5J5
      - key: RECAPTCHA_MIN_SCORE
        value: 0.5
      - key: BCRYPT_ROUNDS
        value: 12
      - key: RATE_LIMIT_MAX
        value: 100
      - key: MAX_FILE_SIZE
        value: 10485760
      - key: UPLOAD_PATH
        value: ./uploads
      - key: ADMIN_EMAIL
        value: <EMAIL>
      - key: ADMIN_PASSWORD
        value: Admin@123456
      - key: ADMIN_NAME
        value: InvoNest Admin
    autoDeploy: true


