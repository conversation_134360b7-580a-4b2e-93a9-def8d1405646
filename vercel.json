{"version": 2, "name": "invonest-frontend", "buildCommand": "cd frontend && npm run build", "outputDirectory": "frontend/.next", "installCommand": "cd frontend && npm install", "framework": "nextjs", "env": {"NEXT_PUBLIC_API_URL": "https://invonest-backend.onrender.com", "NEXT_PUBLIC_RECAPTCHA_SITE_KEY": "6LfjK4grAAAAANb-4AFSjCRQX-1gSAHWPFsTKQ9g", "NEXT_TELEMETRY_DISABLED": "1", "NODE_ENV": "production"}, "regions": ["bom1"], "functions": {"frontend/pages/api/**/*.js": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}