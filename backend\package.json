{"name": "invonest-backend", "version": "1.0.0", "description": "InvoNest - AI-powered invoicing and compliance platform backend", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc && npm run copy-assets", "copy-assets": "cp -r public dist/ || xcopy public dist\\public /E /I /Y || echo 'Assets copied'", "postbuild": "node scripts/initializeDatabase.js", "prebuild": "<PERSON><PERSON><PERSON> dist", "test": "echo \"Error: no test specified\" && exit 1", "seed:compliance": "ts-node src/scripts/seedComplianceData.ts", "seed:notifications": "ts-node src/scripts/seedNotificationData.ts", "seed:dashboard": "ts-node src/scripts/seedDashboardData.ts", "init:db": "node scripts/initializeDatabase.js", "health": "node healthcheck.js"}, "keywords": ["invoicing", "gst", "compliance", "ai", "msme"], "author": "InvoNest Team", "license": "MIT", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "ethers": "^6.13.4", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-slow-down": "^2.1.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "html2canvas": "^1.4.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "mongoose": "^8.15.1", "multer": "^1.4.5-lts.1", "node-cron": "^4.1.0", "node-fetch": "^2.7.0", "nodemailer": "^7.0.3", "pdf.js-extract": "^0.2.1", "pdfkit": "^0.17.1", "puppeteer": "^24.10.0", "qrcode": "^1.5.4", "rate-limiter-flexible": "^5.0.5", "razorpay": "^2.9.6", "redis": "^5.5.6", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/express-validator": "^2.20.33", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/node": "^22.15.30", "@types/node-cron": "^3.0.11", "@types/node-fetch": "^2.6.12", "@types/nodemailer": "^6.4.17", "@types/pdfkit": "^0.14.0", "@types/puppeteer": "^5.4.7", "@types/qrcode": "^1.5.5", "@types/redis": "^4.0.10", "nodemon": "^3.1.10", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}