@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

/* Force light mode for invoice pages to ensure text visibility */
.invoice-page,
.invoice-view,
.invoice-form {
  --background: #ffffff !important;
  --foreground: #171717 !important;
  background: #ffffff !important;
  color: #171717 !important;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }

  /* Override dark mode for invoice pages */
  .invoice-page,
  .invoice-view,
  .invoice-form {
    --background: #ffffff !important;
    --foreground: #171717 !important;
    background: #ffffff !important;
    color: #171717 !important;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Improved focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2;
}

/* Better text contrast */
.text-muted {
  @apply text-gray-700;
}

.text-muted-dark {
  @apply text-gray-600;
}

/* Improved text visibility classes */
.text-visible {
  @apply text-gray-900;
}

.text-secondary-visible {
  @apply text-gray-800;
}

/* Invoice-specific text visibility fixes */
.invoice-page *,
.invoice-view *,
.invoice-form * {
  color: inherit !important;
}

.invoice-page .text-gray-900,
.invoice-view .text-gray-900,
.invoice-form .text-gray-900 {
  color: #111827 !important;
}

.invoice-page .text-gray-800,
.invoice-view .text-gray-800,
.invoice-form .text-gray-800 {
  color: #1f2937 !important;
}

.invoice-page .text-gray-700,
.invoice-view .text-gray-700,
.invoice-form .text-gray-700 {
  color: #374151 !important;
}

.invoice-page .text-gray-600,
.invoice-view .text-gray-600,
.invoice-form .text-gray-600 {
  color: #4b5563 !important;
}

/* Force input text visibility */
.invoice-page input,
.invoice-view input,
.invoice-form input,
.invoice-page textarea,
.invoice-view textarea,
.invoice-form textarea,
.invoice-page select,
.invoice-view select,
.invoice-form select {
  color: #111827 !important;
  background-color: #ffffff !important;
}

.invoice-page input::placeholder,
.invoice-view input::placeholder,
.invoice-form input::placeholder,
.invoice-page textarea::placeholder,
.invoice-view textarea::placeholder,
.invoice-form textarea::placeholder {
  color: #9ca3af !important;
}

/* Better form styling */
.form-label {
  @apply text-gray-900 font-medium;
}

/* Enhanced select styling for better visibility */
.select-enhanced {
  @apply bg-white border border-gray-300 rounded-md px-3 py-2 text-sm text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 hover:border-gray-400 transition-colors;
}

.select-enhanced option {
  @apply text-gray-900 bg-white;
}

/* Improved table text visibility */
.table-header {
  @apply text-gray-900 font-semibold;
}

.table-cell {
  @apply text-gray-900;
}

.table-cell-secondary {
  @apply text-gray-800;
}

/* Mobile-first responsive utilities */
.mobile-only {
  @apply block lg:hidden;
}

.desktop-only {
  @apply hidden lg:block;
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Card hover effects */
.card-hover {
  @apply transition-all duration-200 hover:shadow-lg hover:scale-[1.02];
}

/* Better button styles */
.btn-primary {
  @apply bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2;
}

.btn-secondary {
  @apply bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
}

/* Form improvements */
.form-input {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

/* Additional form input variants */
.form-input:disabled {
  @apply bg-gray-50 text-gray-500 cursor-not-allowed;
}

.form-input:read-only {
  @apply bg-gray-50 text-gray-700;
}

/* Select dropdown styling */
.form-select {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 appearance-none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* Textarea styling */
.form-textarea {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200;
  resize: vertical;
}

/* Status badges */
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-success {
  @apply bg-green-100 text-green-800;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.badge-error {
  @apply bg-red-100 text-red-800;
}

.badge-info {
  @apply bg-blue-100 text-blue-800;
}

/* Dashboard specific styles */
.dashboard-card {
  @apply bg-white rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1;
}

.dashboard-stat-card {
  @apply bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1;
}

.dashboard-action-card {
  @apply rounded-xl border-2 p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1;
}

/* Gradient backgrounds */
.gradient-primary {
  @apply bg-gradient-to-r from-indigo-600 to-purple-600;
}

.gradient-blue {
  @apply bg-gradient-to-br from-blue-500 to-blue-600;
}

.gradient-green {
  @apply bg-gradient-to-br from-green-500 to-green-600;
}

/* Homepage specific styles */
.bg-grid-pattern {
  background-image: radial-gradient(circle, #e5e7eb 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Enhanced hover effects for homepage cards */
.homepage-card {
  @apply bg-white rounded-2xl shadow-lg border border-gray-100 p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2;
}

/* Pricing card styles */
.pricing-card {
  @apply bg-white rounded-2xl shadow-lg border border-gray-200 p-8 hover:shadow-xl transition-all duration-300;
}

.pricing-card-popular {
  @apply bg-white rounded-2xl shadow-xl border-2 border-indigo-500 p-8 hover:shadow-2xl transition-all duration-300 relative;
}

/* Feature icon styles */
.feature-icon {
  @apply w-16 h-16 rounded-xl flex items-center justify-center mb-6;
}

/* Trust indicator styles */
.trust-badge {
  @apply w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4;
}

/* Testimonial card styles */
.testimonial-card {
  @apply bg-white rounded-2xl shadow-lg border border-gray-100 p-8 hover:shadow-xl transition-all duration-300;
}

/* FAQ card styles */
.faq-card {
  @apply bg-white rounded-2xl shadow-sm border border-gray-100 p-8;
}

/* Enhanced button styles for homepage */
.btn-hero-primary {
  @apply w-full sm:w-auto bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1;
}

.btn-hero-secondary {
  @apply w-full sm:w-auto bg-white border-2 border-gray-300 hover:border-indigo-300 text-gray-700 hover:text-indigo-600 px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-200 transform hover:-translate-y-1;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Enhanced focus styles for accessibility */
.focus-visible {
  @apply focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2;
}

.gradient-yellow {
  @apply bg-gradient-to-br from-yellow-500 to-yellow-600;
}

.gradient-purple {
  @apply bg-gradient-to-br from-purple-500 to-purple-600;
}

.gradient-red {
  @apply bg-gradient-to-br from-red-500 to-red-600;
}

/* Enhanced animations */
.animate-slide-up {
  animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Better spacing utilities */
.section-spacing {
  @apply mb-8 lg:mb-10;
}

.card-spacing {
  @apply p-6 lg:p-8;
}

/* Enhanced chat sidebar scrollbar */
.chat-sidebar-scroll {
  scrollbar-width: thin;
  scrollbar-color: #e2e8f0 #f8fafc;
  scroll-behavior: smooth;
}

/* Hide unwanted floating elements */
.hide-floating-widgets {
  /* Hide any floating circular avatars or widgets that might be browser extensions */
  position: relative;
  z-index: 9999;
}

/* Override any external floating elements */
body > div[style*="position: fixed"]:not([class*="dashboard"]):not([class*="mobile"]):not([class*="chat"]) {
  display: none !important;
}

/* Hide specific floating avatar patterns */
div[style*="position: fixed"][style*="border-radius: 50%"]:not([class]) {
  display: none !important;
}

/* Hide common extension floating elements */
[id*="extension"]:not([class*="dashboard"]),
[class*="extension-widget"],
[class*="floating-avatar"],
div[style*="position: fixed"][style*="z-index: 999"]:not([class*="dashboard"]):not([class*="chat"]) {
  display: none !important;
}

/* Hide elements that look like the "N" avatar */
div[style*="position: fixed"] > div[style*="border-radius: 50%"] {
  display: none !important;
}

/* Hide shadow-root elements that might contain floating widgets */
*:has(#shadow-root) {
  display: none !important;
}

/* Hide Next.js development overlays and browser extension elements */
[data-nextjs-toast],
[data-nextjs-toast-wrapper],
div[style*="z-index: 2147483647"],
div[style*="z-index: 999999"],
div[style*="position: fixed"][style*="pointer-events: none"],
/* Hide Next.js build activity indicator */
[data-nextjs-build-indicator],
div[style*="position: fixed"][style*="bottom"][style*="right"] > div:has(svg),
div[style*="position: fixed"][style*="bottom"][style*="left"] > div:has(svg),
/* Hide Turbopack/Next.js development indicators */
div[data-turbopack-indicator],
div[data-nextjs-dev-indicator] {
  display: none !important;
}

/* Target specific shadow DOM elements */
::part(floating-widget),
::part(extension-avatar) {
  display: none !important;
}

/* More aggressive hiding of potential browser extension elements */
body > div:not([class]):not([id])[style*="position: fixed"] {
  display: none !important;
}

/* Hide elements with extremely high z-index (common in extensions) */
*[style*="z-index: 2147483647"],
*[style*="z-index: 999999999"],
*[style*="z-index: 2147483646"] {
  display: none !important;
}

/* Hide any circular floating elements that appear in corners */
div[style*="position: fixed"][style*="bottom"][style*="right"] > div[style*="border-radius: 50%"]:not([class*="chat"]):not([class*="dashboard"]) {
  display: none !important;
}

.chat-sidebar-scroll::-webkit-scrollbar {
  width: 6px;
}

.chat-sidebar-scroll::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 6px;
  margin: 4px 0;
}

.chat-sidebar-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #e2e8f0 0%, #cbd5e1 100%);
  border-radius: 6px;
  border: 1px solid #f1f5f9;
  transition: all 0.2s ease;
}

.chat-sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #cbd5e1 0%, #94a3b8 100%);
  transform: scaleX(1.2);
}

.chat-sidebar-scroll::-webkit-scrollbar-thumb:active {
  background: #94a3b8;
}

/* Enhanced chat messages scroll area */
.chat-messages-scroll {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db transparent;
  scroll-behavior: smooth;
}

.chat-messages-scroll::-webkit-scrollbar {
  width: 6px;
}

.chat-messages-scroll::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

.chat-messages-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #d1d5db 0%, #9ca3af 100%);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.chat-messages-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #9ca3af 0%, #6b7280 100%);
  transform: scaleX(1.3);
}

/* Smooth scrolling for chat containers */
.chat-container {
  scroll-behavior: smooth;
}

/* Fixed sidebar and navigation styles */
.fixed-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 16rem; /* 64 * 0.25rem = 16rem */
  z-index: 50;
  background: white;
  box-shadow: 4px 0 15px -3px rgba(0, 0, 0, 0.1), 2px 0 6px -2px rgba(0, 0, 0, 0.05);
  border-right: 1px solid #e5e7eb;
}

.fixed-mobile-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 30;
  background: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border-bottom: 1px solid #e5e7eb;
}

/* Main content with proper spacing for fixed sidebar */
.main-content-with-sidebar {
  padding-left: 16rem; /* Space for fixed sidebar on desktop */
  padding-top: 4rem; /* Space for fixed mobile nav */
  min-height: 100vh;
}

@media (max-width: 1023px) {
  .main-content-with-sidebar {
    padding-left: 0; /* Remove left padding on mobile */
  }
}

/* Smooth transitions for sidebar */
.sidebar-transition {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced scrollbar for main content */
.main-content-scroll {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
  scroll-behavior: smooth;
}

.main-content-scroll::-webkit-scrollbar {
  width: 8px;
}

.main-content-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.main-content-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #cbd5e1 0%, #94a3b8 100%);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.main-content-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #94a3b8 0%, #64748b 100%);
}

/* Line clamp utility for better text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

/* Chat conversation item animations */
.chat-conversation-item {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(0);
}

.chat-conversation-item:hover {
  transform: translateY(-1px) translateZ(0);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.chat-conversation-item.active {
  transform: translateY(-1px) translateZ(0);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
}

/* Fade in animation for chat messages */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.3s ease-out;
}

/* Mobile App-like Styles */
.safe-area-inset {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

.safe-area-pt {
  padding-top: env(safe-area-inset-top);
}

.safe-area-pb {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-pl {
  padding-left: env(safe-area-inset-left);
}

.safe-area-pr {
  padding-right: env(safe-area-inset-right);
}

/* Touch-friendly interactions */
.touch-manipulation {
  touch-action: manipulation;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Mobile-specific scrolling */
.mobile-scroll {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

/* Pull-to-refresh indicator */
.pull-to-refresh {
  position: relative;
  overflow: hidden;
}

.pull-to-refresh::before {
  content: '';
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 30px;
  border: 3px solid #e5e7eb;
  border-top-color: #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.pull-to-refresh.refreshing::before {
  opacity: 1;
}

@keyframes spin {
  to {
    transform: translateX(-50%) rotate(360deg);
  }
}

/* Mobile card styles */
.mobile-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  margin-bottom: 4px;
}

.mobile-card:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.mobile-card .swipe-content {
  background: white;
  position: relative;
  z-index: 10;
}

/* Mobile button styles */
.mobile-btn {
  min-height: 44px;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.2s ease;
  touch-action: manipulation;
}

.mobile-btn:active {
  transform: scale(0.96);
}

.mobile-btn-primary {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.mobile-btn-primary:active {
  box-shadow: 0 2px 6px rgba(79, 70, 229, 0.3);
}

.mobile-btn-secondary {
  background: white;
  color: #4f46e5;
  border: 2px solid #4f46e5;
}

/* Mobile input styles */
.mobile-input {
  min-height: 44px;
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  padding: 12px 16px;
  font-size: 16px; /* Prevents zoom on iOS */
  transition: all 0.2s ease;
  background: white;
  color: #1f2937;
  width: 100%;
}

.mobile-input:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  outline: none;
}

.mobile-input::placeholder {
  color: #9ca3af;
}

/* Mobile list item styles */
.mobile-list-item {
  padding: 16px;
  background: white;
  border-bottom: 1px solid #f3f4f6;
  transition: all 0.2s ease;
  touch-action: manipulation;
}

.mobile-list-item:active {
  background: #f9fafb;
  transform: scale(0.99);
}

.mobile-list-item:last-child {
  border-bottom: none;
}

/* Swipe actions */
.swipe-actions {
  position: relative;
  overflow: hidden;
  border-radius: 16px;
  background: white;
}

.swipe-content {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: white;
  position: relative;
  z-index: 10;
  border-radius: 16px;
  width: 100%;
}

.swipe-actions-left,
.swipe-actions-right {
  position: absolute;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  z-index: 1;
  border-radius: 16px;
}

.swipe-actions-left {
  left: 0;
  background: linear-gradient(90deg, #10b981, #059669);
}

.swipe-actions-right {
  right: 0;
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

/* Mobile modal styles */
.mobile-modal {
  position: fixed;
  inset: 0;
  z-index: 60;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding: 16px;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.mobile-modal-content {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 20px 20px 0 0;
  padding: 24px;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-modal.open .mobile-modal-content {
  transform: translateY(0);
}

/* Mobile bottom sheet handle */
.bottom-sheet-handle {
  width: 40px;
  height: 4px;
  background: #d1d5db;
  border-radius: 2px;
  margin: 0 auto 16px;
}

/* Haptic feedback simulation */
@keyframes haptic-light {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

@keyframes haptic-medium {
  0% { transform: scale(1); }
  25% { transform: scale(1.03); }
  75% { transform: scale(0.98); }
  100% { transform: scale(1); }
}

.haptic-light {
  animation: haptic-light 0.1s ease;
}

.haptic-medium {
  animation: haptic-medium 0.15s ease;
}

/* Ensure text visibility on mobile cards */
.mobile-card * {
  color: inherit;
}

.mobile-card h1,
.mobile-card h2,
.mobile-card h3,
.mobile-card h4,
.mobile-card h5,
.mobile-card h6 {
  color: #1f2937;
}

.mobile-card p,
.mobile-card span,
.mobile-card div {
  color: inherit;
}

/* Fix for mobile filter dropdowns */
.mobile-input select {
  background: white;
  color: #1f2937;
}

.mobile-input option {
  background: white;
  color: #1f2937;
}

/* Ensure proper spacing between mobile cards */
.mobile-card + .mobile-card {
  margin-top: 16px;
}

/* Fix for mobile card content visibility */
.mobile-card .swipe-content > div {
  background: white;
  color: #1f2937;
}

/* Mobile text color fixes */
.mobile-card .text-gray-900 {
  color: #111827 !important;
}

.mobile-card .text-gray-700 {
  color: #374151 !important;
}

.mobile-card .text-gray-500 {
  color: #6b7280 !important;
}

/* Ensure mobile card content is always visible */
.mobile-card .swipe-content {
  background: white !important;
  color: #1f2937 !important;
}

.mobile-card .swipe-content * {
  color: inherit;
}

.mobile-card .swipe-content h1,
.mobile-card .swipe-content h2,
.mobile-card .swipe-content h3,
.mobile-card .swipe-content h4,
.mobile-card .swipe-content h5,
.mobile-card .swipe-content h6 {
  color: #1f2937 !important;
}

/* Fix for status badges and other colored elements */
.mobile-card .bg-green-100 {
  background-color: #dcfce7 !important;
  color: #166534 !important;
}

.mobile-card .bg-blue-100 {
  background-color: #dbeafe !important;
  color: #1e40af !important;
}

.mobile-card .bg-yellow-100 {
  background-color: #fef3c7 !important;
  color: #92400e !important;
}

.mobile-card .bg-red-100 {
  background-color: #fee2e2 !important;
  color: #991b1b !important;
}

.mobile-card .bg-purple-100 {
  background-color: #f3e8ff !important;
  color: #7c2d12 !important;
}
